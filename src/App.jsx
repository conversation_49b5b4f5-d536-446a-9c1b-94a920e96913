import React, { useState, useEffect, useCallback } from 'react';
import { initializeApp } from 'firebase/app';
import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } from 'firebase/auth';
import { getFirestore, collection, query, onSnapshot, doc, addDoc, updateDoc, deleteDoc, serverTimestamp, where } from 'firebase/firestore';

// Import utilities and context
import { formatCurrency, formatDate } from './utils/formatters';
import { AuthContext } from './context/AuthContext';

// Import modal components
import ConfirmationModal from './components/modals/ConfirmationModal';
import MessageBox from './components/modals/MessageBox';
import HotelModal from './components/modals/HotelModal';
import RoomModal from './components/modals/RoomModal';
import ReservationModal from './components/modals/ReservationModal';
import ReservationDetailModal from './components/modals/ReservationDetailModal';

// Main App Component
const App = () => {
 // State for active navigation tab
 const [activeTab, setActiveTab] = useState('dashboard');
 // Firebase related states
 const [app, setApp] = useState(null);
 const [db, setDb] = useState(null);
 const [auth, setAuth] = useState(null);
 const [userId, setUserId] = useState(null);
 const [isAuthReady, setIsAuthReady] = useState(false); // Tracks if Firebase auth is initialized

 // Hotel management states
 const [hotels, setHotels] = useState([]);
 const [selectedHotelId, setSelectedHotelId] = useState(null);
 const [showHotelModal, setShowHotelModal] = useState(false);
 const [currentHotel, setCurrentHotel] = useState(null); // Used for editing hotel

 // Data states for rooms, reservations, and finances
 const [rooms, setRooms] = useState([]);
 const [reservations, setReservations] = useState([]);
 const [finances, setFinances] = useState([]);

 // Modals visibility states
 const [showRoomModal, setShowRoomModal] = useState(false);
 const [currentRoom, setCurrentRoom] = useState(null); // Used for editing room
 const [showReservationModal, setShowReservationModal] = useState(false);
 const [currentReservation, setCurrentReservation] = useState(null); // Used for editing reservation
 const [showReservationDetailModal, setShowReservationDetailModal] = useState(false); // New: for invoice/detail view
 const [showConfirmModal, setShowConfirmModal] = useState(false);
 const [confirmAction, setConfirmAction] = useState(null); // Function to execute on confirmation
 const [confirmMessage, setConfirmMessage] = useState('');

 // Custom alert/message box state
 const [messageBox, setMessageBox] = useState({ visible: false, message: '', type: 'info' });

 // Function to show custom message box
 const showMessage = (message, type = 'info', duration = 3000) => {
   setMessageBox({ visible: true, message, type });
   setTimeout(() => {
     setMessageBox({ visible: false, message: '', type: 'info' });
   }, duration);
 };

 // --- Firebase Initialization and Authentication ---
 useEffect(() => {
   try {
     const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};
     const initializedApp = initializeApp(firebaseConfig);
     const firestoreDb = getFirestore(initializedApp);
     const firebaseAuth = getAuth(initializedApp);

     setApp(initializedApp);
     setDb(firestoreDb);
     setAuth(firebaseAuth);

     const unsubscribe = onAuthStateChanged(firebaseAuth, async (user) => {
       if (user) {
         setUserId(user.uid);
         showMessage('Autentikasi berhasil.', 'success');
       } else {
         // Sign in anonymously if no user and no custom token provided
         if (typeof __initial_auth_token === 'undefined' || !__initial_auth_token) {
           try {
             await signInAnonymously(firebaseAuth);
             setUserId(firebaseAuth.currentUser?.uid || crypto.randomUUID());
             showMessage('Masuk secara anonim berhasil.', 'info');
           } catch (anonError) {
             console.error("Kesalahan saat masuk secara anonim:", anonError);
             showMessage('Gagal masuk secara anonim.', 'error');
             setUserId(crypto.randomUUID()); // Fallback to a random ID if anonymous sign-in fails
           }
         }
       }
       setIsAuthReady(true); // Auth state is now ready
     });

     // Handle custom token if available
     if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
       signInWithCustomToken(firebaseAuth, __initial_auth_token)
         .then((userCredential) => {
           setUserId(userCredential.user.uid);
           showMessage('Masuk dengan token kustom berhasil.', 'success');
         })
         .catch((error) => {
           console.error("Kesalahan saat masuk dengan token kustom:", error);
           showMessage('Gagal masuk dengan token kustom. Mencoba masuk secara anonim...', 'error');
           // Fallback to anonymous if custom token fails
           signInAnonymously(firebaseAuth)
             .then(() => {
               setUserId(firebaseAuth.currentUser?.uid || crypto.randomUUID());
               showMessage('Masuk secara anonim berhasil sebagai pengganti.', 'info');
             })
             .catch(err => {
               console.error("Kesalahan saat masuk secara anonim:", err);
               showMessage('Gagal masuk secara anonim.', 'error');
             });
         })
         .finally(() => setIsAuthReady(true));
     }

     return () => unsubscribe(); // Cleanup auth listener on component unmount
   } catch (e) {
     console.error("Gagal menginisialisasi Firebase:", e);
     showMessage('Gagal menginisialisasi Firebase.', 'error');
     setIsAuthReady(true); // Still set to true to avoid infinite loading, but data might not load
   }
 }, []); // Run once on component mount

 // --- Firestore Data Listeners ---
 useEffect(() => {
   if (!db || !userId || !isAuthReady) return; // Wait for Firebase and Auth to be ready

   const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
   const userPrivatePath = `artifacts/${appId}/users/${userId}`;

   // Hotels Listener
   const hotelsCollectionRef = collection(db, `${userPrivatePath}/hotels`);
   const unsubscribeHotels = onSnapshot(hotelsCollectionRef, (snapshot) => {
     const hotelsData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setHotels(hotelsData);
     // Automatically select the first hotel if none is selected
     if (!selectedHotelId && hotelsData.length > 0) {
       setSelectedHotelId(hotelsData[0].id);
     } else if (hotelsData.length === 0) {
       setSelectedHotelId(null);
     }
   }, (error) => {
     console.error("Kesalahan saat mengambil data hotel:", error);
     showMessage('Gagal memuat data hotel.', 'error');
   });

   return () => {
     unsubscribeHotels();
   };
 }, [db, userId, isAuthReady, selectedHotelId]); // Re-run if db, userId, isAuthReady, or selectedHotelId changes

 // Listener for rooms, reservations, and finances based on selected hotel
 useEffect(() => {
   if (!db || !userId || !isAuthReady || !selectedHotelId) {
     setRooms([]);
     setReservations([]);
     setFinances([]);
     return; // Clear data if no hotel is selected or not ready
   }

   const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
   const hotelDataPath = `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}`;

   // Rooms Listener
   const roomsCollectionRef = collection(db, `${hotelDataPath}/rooms`);
   const unsubscribeRooms = onSnapshot(roomsCollectionRef, (snapshot) => {
     const roomsData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setRooms(roomsData);
   }, (error) => {
     console.error("Kesalahan saat mengambil data kamar:", error);
     showMessage('Gagal memuat data kamar.', 'error');
   });

   // Reservations Listener
   const reservationsCollectionRef = collection(db, `${hotelDataPath}/reservations`);
   const unsubscribeReservations = onSnapshot(reservationsCollectionRef, (snapshot) => {
     const reservationsData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setReservations(reservationsData);
   }, (error) => {
     console.error("Kesalahan saat mengambil data reservasi:", error);
     showMessage('Gagal memuat data reservasi.', 'error');
   });

   // Finances Listener
   const financesCollectionRef = collection(db, `${hotelDataPath}/finances`);
   const unsubscribeFinances = onSnapshot(financesCollectionRef, (snapshot) => {
     const financesData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setFinances(financesData);
   }, (error) => {
     console.error("Kesalahan saat mengambil data keuangan:", error);
     showMessage('Gagal memuat data keuangan.', 'error');
   });

   return () => {
     unsubscribeRooms();
     unsubscribeReservations();
     unsubscribeFinances();
   };
 }, [db, userId, isAuthReady, selectedHotelId]); // Re-run if db, userId, isAuthReady, or selectedHotelId changes

 // --- CRUD Operations for Hotels ---
 const addHotel = async (hotelData) => {
   if (!db || !userId) { showMessage('Basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     const docRef = await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels`), {
       ...hotelData,
       createdAt: serverTimestamp(),
       userId: userId,
     });
     showMessage('Hotel berhasil ditambahkan!', 'success');
     setSelectedHotelId(docRef.id); // Select the new hotel
     setShowHotelModal(false);
   } catch (e) {
     console.error("Kesalahan saat menambahkan hotel: ", e);
     showMessage('Gagal menambahkan hotel.', 'error');
   }
 };

 const updateHotel = async (id, hotelData) => {
   if (!db || !userId) { showMessage('Basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await updateDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels`, id), {
       ...hotelData,
       updatedAt: serverTimestamp(),
     });
     showMessage('Hotel berhasil diperbarui!', 'success');
     setShowHotelModal(false);
   } catch (e) {
     console.error("Kesalahan saat memperbarui hotel: ", e);
     showMessage('Gagal memperbarui hotel.', 'error');
   }
 };

 const deleteHotel = async (id) => {
   if (!db || !userId) { showMessage('Basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     // WARNING: Deleting a hotel document currently does NOT automatically delete its sub-collections (rooms, reservations, finances).
     // These will become "orphaned" in Firestore. For a production app, consider using Firebase Cloud Functions
     // to implement cascading deletes when a hotel document is deleted.
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels`, id));
     showMessage('Hotel berhasil dihapus! (Catatan: data terkait kamar/reservasi/keuangan mungkin masih ada)', 'success', 5000);
     setShowConfirmModal(false); // Close confirm modal
     if (selectedHotelId === id) {
       setSelectedHotelId(hotels.length > 1 ? hotels[0].id : null); // Select another hotel or clear
     }
   } catch (e) {
     console.error("Kesalahan saat menghapus hotel: ", e);
     showMessage('Gagal menghapus hotel.', 'error');
   }
 };


 // --- CRUD Operations for Rooms ---
 const addRoom = async (roomData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     const docRef = await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/rooms`), {
       ...roomData,
       createdAt: serverTimestamp(),
       userId: userId,
       hotelId: selectedHotelId,
     });
     showMessage('Kamar berhasil ditambahkan!', 'success');
     console.log("Kamar ditambahkan dengan ID: ", docRef.id);
     setShowRoomModal(false);
   } catch (e) {
     console.error("Kesalahan saat menambahkan kamar: ", e);
     showMessage('Gagal menambahkan kamar.', 'error');
   }
 };

 const updateRoom = async (id, roomData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await updateDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/rooms`, id), {
       ...roomData,
       updatedAt: serverTimestamp(),
     });
     showMessage('Kamar berhasil diperbarui!', 'success');
     console.log("Kamar diperbarui dengan ID: ", id);
     setShowRoomModal(false);
   } catch (e) {
     console.error("Kesalahan saat memperbarui kamar: ", e);
     showMessage('Gagal memperbarui kamar.', 'error');
   }
 };

 const deleteRoom = async (id) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/rooms`, id));
     showMessage('Kamar berhasil dihapus!', 'success');
     console.log("Kamar dihapus dengan ID: ", id);
     setShowConfirmModal(false); // Close confirm modal
   } catch (e) {
     console.error("Kesalahan saat menghapus kamar: ", e);
     showMessage('Gagal menghapus kamar.', 'error');
   }
 };

 // --- CRUD Operations for Reservations ---
 const addReservation = async (reservationData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/reservations`), {
       ...reservationData,
       checkInDate: new Date(reservationData.checkInDate), // Convert string to Date object
       checkOutDate: new Date(reservationData.checkOutDate), // Convert string to Date object
       createdAt: serverTimestamp(),
       userId: userId,
       hotelId: selectedHotelId,
     });
     showMessage('Reservasi berhasil ditambahkan!', 'success');
     setShowReservationModal(false);
   } catch (e) {
     console.error("Kesalahan saat menambahkan reservasi: ", e);
     showMessage('Gagal menambahkan reservasi.', 'error');
   }
 };

 const updateReservation = async (id, reservationData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await updateDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/reservations`, id), {
       ...reservationData,
       checkInDate: new Date(reservationData.checkInDate), // Ensure Date objects
       checkOutDate: new Date(reservationData.checkOutDate), // Ensure Date objects
       updatedAt: serverTimestamp(),
     });
     showMessage('Reservasi berhasil diperbarui!', 'success');
     setShowReservationModal(false);
   } catch (e) {
     console.error("Kesalahan saat memperbarui reservasi: ", e);
     showMessage('Gagal memperbarui reservasi.', 'error');
   }
 };

 const deleteReservation = async (id) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/reservations`, id));
     showMessage('Reservasi berhasil dihapus!', 'success');
     setShowConfirmModal(false); // Close confirm modal
   } catch (e) {
     console.error("Kesalahan saat menghapus reservasi: ", e);
     showMessage('Gagal menghapus reservasi.', 'error');
   }
 };

 // --- CRUD Operations for Finances (simplified) ---
 const addFinanceEntry = async (entryData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/finances`), {
       ...entryData,
       date: new Date(entryData.date), // Convert string to Date object
       createdAt: serverTimestamp(),
       userId: userId,
       hotelId: selectedHotelId,
     });
     showMessage('Entri keuangan berhasil ditambahkan!', 'success');
   } catch (e) {
     console.error("Kesalahan saat menambahkan entri keuangan: ", e);
     showMessage('Gagal menambahkan entri keuangan.', 'error');
   }
 };

 const deleteFinanceEntry = async (id) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/finances`, id));
     showMessage('Entri keuangan berhasil dihapus!', 'success');
     setShowConfirmModal(false);
   } catch (e) {
     console.error("Kesalahan saat menghapus entri keuangan: ", e);
     showMessage('Gagal menghapus entri keuangan.', 'error');
   }
 };





 // --- Room Modal Component ---
 const RoomModal = ({ room, onClose, onSave, showMessage }) => {
   const [formData, setFormData] = useState({
     roomNumber: room?.roomNumber || '',
     roomType: room?.roomType || 'Standard',
     capacity: room?.capacity || 2,
     pricePerNight: room?.pricePerNight || 0,
     status: room?.status || 'available',
   });

   const handleChange = (e) => {
     const { name, value, type } = e.target;
     setFormData(prev => ({
       ...prev,
       [name]: type === 'number' ? parseFloat(value) || 0 : value
     }));
   };

   const handleSubmit = (e) => {
     e.preventDefault();
     if (!formData.roomNumber.trim()) {
       showMessage('Nomor kamar tidak boleh kosong.', 'error');
       return;
     }
     if (formData.capacity <= 0) {
       showMessage('Kapasitas harus lebih besar dari 0.', 'error');
       return;
     }
     if (formData.pricePerNight < 0) {
       showMessage('Harga per malam tidak boleh negatif.', 'error');
       return;
     }
     onSave(room ? room.id : null, formData);
   };

   return (
     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
       <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg border border-gray-200">
         <h2 className="text-2xl font-bold text-gray-800 mb-6">{room ? 'Edit Kamar' : 'Tambah Kamar Baru'}</h2>
         <form onSubmit={handleSubmit} className="space-y-4">
           <div>
             <label htmlFor="roomNumber" className="block text-sm font-medium text-gray-700 mb-1">Nomor Kamar</label>
             <input
               type="text"
               id="roomNumber"
               name="roomNumber"
               value={formData.roomNumber}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               required
             />
           </div>
           <div>
             <label htmlFor="roomType" className="block text-sm font-medium text-gray-700 mb-1">Tipe Kamar</label>
             <select
               id="roomType"
               name="roomType"
               value={formData.roomType}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="Standard">Standard</option>
               <option value="Double">Double</option>
               <option value="Twin">Twin</option>
               <option value="Triple">Triple</option>
               <option value="Quad">Quad</option>
               <option value="Suite">Suite</option>
             </select>
           </div>
           <div>
             <label htmlFor="capacity" className="block text-sm font-medium text-gray-700 mb-1">Kapasitas (Orang)</label>
             <input
               type="number"
               id="capacity"
               name="capacity"
               value={formData.capacity}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               min="1"
               required
             />
           </div>
           <div>
             <label htmlFor="pricePerNight" className="block text-sm font-medium text-gray-700 mb-1">Harga per Malam</label>
             <input
               type="number"
               id="pricePerNight"
               name="pricePerNight"
               value={formData.pricePerNight}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               min="0"
               step="0.01"
               required
             />
           </div>
           <div>
             <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
             <select
               id="status"
               name="status"
               value={formData.status}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="available">Tersedia</option>
               <option value="occupied">Terisi</option>
               <option value="maintenance">Perawatan</option>
             </select>
           </div>
           <div className="flex justify-end gap-3 pt-4">
             <button
               type="button"
               onClick={onClose}
               className="px-5 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-75"
             >
               Batal
             </button>
             <button
               type="submit"
               className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
             >
               Simpan
             </button>
           </div>
         </form>
       </div>
     </div>
   );
 };

 // --- Reservation Modal Component ---
 const ReservationModal = ({ reservation, rooms, onClose, onSave, showMessage }) => {
   const today = new Date().toISOString().split('T')[0];
   const [formData, setFormData] = useState({
     guestName: reservation?.guestName || '',
     roomNumber: reservation?.roomNumber || '',
     checkInDate: reservation?.checkInDate ? new Date(reservation.checkInDate.toDate()).toISOString().split('T')[0] : today,
     checkOutDate: reservation?.checkOutDate ? new Date(reservation.checkOutDate.toDate()).toISOString().split('T')[0] : today,
     numberOfGuests: reservation?.numberOfGuests || 1,
     mealPlan: reservation?.mealPlan || 'None',
     status: reservation?.status || 'confirmed',
     totalAmount: reservation?.totalAmount || 0,
     paidAmount: reservation?.paidAmount || 0,
   });

   const [outstandingAmount, setOutstandingAmount] = useState(0);

   // Effect to calculate totalAmount when roomNumber, dates, or guests change
   useEffect(() => {
     const calculateTotalAmount = () => {
       const selectedRoom = rooms.find(r => r.roomNumber === formData.roomNumber);
       if (!selectedRoom || !formData.checkInDate || !formData.checkOutDate) {
         return 0;
       }

       const checkIn = new Date(formData.checkInDate);
       const checkOut = new Date(formData.checkOutDate);

       if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime()) || checkOut <= checkIn) {
         return 0;
       }

       const diffTime = Math.abs(checkOut - checkIn);
       const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
       let basePrice = selectedRoom.pricePerNight * diffDays;

       // Add meal plan cost (simplified: fixed cost per guest per day)
       const mealCostPerGuestPerDay = {
         'BB': 50000,   // Example: Rp 50.000 for Bed & Breakfast
         'HB': 100000,  // Example: Rp 100.000 for Half Board
         'FB': 150000,  // Example: Rp 150.000 for Full Board
         'None': 0
       };
       basePrice += (mealCostPerGuestPerDay[formData.mealPlan] || 0) * formData.numberOfGuests * diffDays;

       return basePrice;
     };

     // Only auto-calculate for new reservations or if totalAmount is 0 for existing ones
     if (!reservation || (reservation && reservation.totalAmount === 0)) {
       const calculatedAmount = calculateTotalAmount();
       setFormData(prev => ({
         ...prev,
         totalAmount: calculatedAmount,
       }));
     }

     setOutstandingAmount((parseFloat(formData.totalAmount) || 0) - (parseFloat(formData.paidAmount) || 0));

   }, [formData.roomNumber, formData.checkInDate, formData.checkOutDate, formData.numberOfGuests, formData.mealPlan, formData.totalAmount, formData.paidAmount, rooms, reservation]);


   const handleChange = (e) => {
     const { name, value, type } = e.target;
     setFormData(prev => ({
       ...prev,
       [name]: type === 'number' ? parseFloat(value) || 0 : value
     }));
   };

   const handleSubmit = (e) => {
     e.preventDefault();
     // Basic validation
     if (!formData.guestName.trim()) {
       showMessage('Nama tamu tidak boleh kosong.', 'error');
       return;
     }
     if (!formData.roomNumber) {
       showMessage('Nomor kamar harus dipilih.', 'error');
       return;
     }
     const checkIn = new Date(formData.checkInDate);
     const checkOut = new Date(formData.checkOutDate);

     if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
       showMessage('Format tanggal check-in/check-out tidak valid.', 'error');
       return;
     }
     if (checkOut <= checkIn) {
       showMessage('Tanggal check-out harus setelah tanggal check-in.', 'error');
       return;
     }
     if (formData.numberOfGuests <= 0) {
       showMessage('Jumlah tamu harus lebih besar dari 0.', 'error');
       return;
     }
     if (formData.totalAmount < 0) {
       showMessage('Total biaya tidak boleh negatif.', 'error');
       return;
     }
     if (formData.paidAmount < 0) {
       showMessage('Jumlah dibayar tidak boleh negatif.', 'error');
       return;
     }

     onSave(reservation ? reservation.id : null, { ...formData, outstandingAmount });
   };

   // Filter available rooms (simplified: just list all for now, complex logic would check dates)
   const availableRoomNumbers = rooms.map(room => room.roomNumber);

   return (
     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
       <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg border border-gray-200">
         <h2 className="text-2xl font-bold text-gray-800 mb-6">{reservation ? 'Edit Reservasi' : 'Tambah Reservasi Baru'}</h2>
         <form onSubmit={handleSubmit} className="space-y-4">
           <div>
             <label htmlFor="guestName" className="block text-sm font-medium text-gray-700 mb-1">Nama Tamu</label>
             <input
               type="text"
               id="guestName"
               name="guestName"
               value={formData.guestName}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               required
             />
           </div>
           <div>
             <label htmlFor="roomNumber" className="block text-sm font-medium text-gray-700 mb-1">Nomor Kamar</label>
             <select
               id="roomNumber"
               name="roomNumber"
               value={formData.roomNumber}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               required
             >
               <option value="">Pilih Kamar</option>
               {availableRoomNumbers.map(num => (
                 <option key={num} value={num}>{num}</option>
               ))}
             </select>
           </div>
           <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
             <div>
               <label htmlFor="checkInDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal Check-in</label>
               <input
                 type="date"
                 id="checkInDate"
                 name="checkInDate"
                 value={formData.checkInDate}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 required
               />
             </div>
             <div>
               <label htmlFor="checkOutDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal Check-out</label>
               <input
                 type="date"
                 id="checkOutDate"
                 name="checkOutDate"
                 value={formData.checkOutDate}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 required
               />
             </div>
           </div>
           <div>
             <label htmlFor="numberOfGuests" className="block text-sm font-medium text-gray-700 mb-1">Jumlah Tamu</label>
             <input
               type="number"
               id="numberOfGuests"
               name="numberOfGuests"
               value={formData.numberOfGuests}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               min="1"
               required
             />
           </div>
           <div>
             <label htmlFor="mealPlan" className="block text-sm font-medium text-gray-700 mb-1">Paket Makanan</label>
             <select
               id="mealPlan"
               name="mealPlan"
               value={formData.mealPlan}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="None">Tidak Ada</option>
               <option value="BB">BB (Bed & Breakfast)</option>
               <option value="HB">HB (Half Board)</option>
               <option value="FB">FB (Full Board)</option>
             </select>
           </div>
           <div>
             <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
             <select
               id="status"
               name="status"
               value={formData.status}
               onChange={handleChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="confirmed">Dikonfirmasi</option>
               <option value="checked-in">Check-in</option>
               <option value="checked-out">Check-out</option>
               <option value="cancelled">Dibatalkan</option>
             </select>
           </div>
           <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
             <div>
               <label htmlFor="totalAmount" className="block text-sm font-medium text-gray-700 mb-1">Total Biaya</label>
               <input
                 type="number"
                 id="totalAmount"
                 name="totalAmount"
                 value={formData.totalAmount}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 min="0"
                 step="0.01"
                 required
               />
             </div>
             <div>
               <label htmlFor="paidAmount" className="block text-sm font-medium text-gray-700 mb-1">Jumlah Dibayar</label>
               <input
                 type="number"
                 id="paidAmount"
                 name="paidAmount"
                 value={formData.paidAmount}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 min="0"
                 step="0.01"
               />
             </div>
           </div>
           <div className="text-right text-gray-700 font-semibold">
             Sisa Tunggakan: {formatCurrency(outstandingAmount)}
           </div>
           <div className="flex justify-end gap-3 pt-4">
             <button
               type="button"
               onClick={onClose}
               className="px-5 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-75"
             >
               Batal
             </button>
             <button
               type="submit"
               className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
             >
               Simpan
             </button>
           </div>
         </form>
       </div>
     </div>
   );
 };

 // --- Reservation Detail/Invoice Modal Component ---
 const ReservationDetailModal = ({ reservation, onClose }) => {
   if (!reservation) return null;

   const selectedHotel = hotels.find(h => h.id === selectedHotelId);
   const hotelName = selectedHotel ? selectedHotel.name : 'N/A';
   const hotelAddress = selectedHotel ? selectedHotel.address : 'N/A';
   const hotelContact = selectedHotel ? selectedHotel.contact : 'N/A';

   return (
     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
       <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl border border-gray-200 overflow-y-auto max-h-[90vh]">
         <h2 className="text-2xl font-bold text-gray-800 mb-6 border-b pb-3">Detail Reservasi & Tagihan</h2>

         <div className="mb-6 border-b pb-4">
           <h3 className="text-xl font-semibold text-blue-700 mb-2">{hotelName}</h3>
           <p className="text-gray-600 text-sm">{hotelAddress}</p>
           <p className="text-gray-600 text-sm">Telp: {hotelContact}</p>
         </div>

         <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
           <div>
             <p className="font-semibold text-gray-800">Detail Tamu:</p>
             <p>Nama: {reservation.guestName}</p>
             <p>Jumlah Tamu: {reservation.numberOfGuests}</p>
           </div>
           <div>
             <p className="font-semibold text-gray-800">Detail Reservasi:</p>
             <p>Nomor Reservasi: {reservation.id}</p>
             <p>Kamar: {reservation.roomNumber}</p>
             <p>Check-in: {formatDate(reservation.checkInDate)}</p>
             <p>Check-out: {formatDate(reservation.checkOutDate)}</p>
             <p>Paket Makanan: {reservation.mealPlan}</p>
             <p>Status: {reservation.status}</p>
           </div>
         </div>

         <div className="mb-6">
           <h3 className="text-xl font-semibold text-gray-800 mb-3">Rincian Biaya</h3>
           <div className="space-y-2">
             <div className="flex justify-between border-b pb-1">
               <span className="text-gray-700">Total Biaya Reservasi:</span>
               <span className="font-semibold">{formatCurrency(reservation.totalAmount)}</span>
             </div>
             <div className="flex justify-between border-b pb-1">
               <span className="text-gray-700">Jumlah Dibayar:</span>
               <span className="font-semibold">{formatCurrency(reservation.paidAmount)}</span>
             </div>
             <div className="flex justify-between pt-2">
               <span className="text-lg font-bold text-gray-900">Sisa Tunggakan:</span>
               <span className="text-lg font-bold text-red-600">{formatCurrency(reservation.outstandingAmount)}</span>
             </div>
           </div>
         </div>

         <div className="flex justify-end pt-4">
           <button
             onClick={onClose}
             className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
           >
             Tutup
           </button>
         </div>
       </div>
     </div>
   );
 };


 // --- Dashboard Component ---
 const Dashboard = useCallback(() => {
   const totalRooms = rooms.length;
   const occupiedRooms = rooms.filter(room => room.status === 'occupied').length;
   const availableRooms = rooms.filter(room => room.status === 'available').length;
   const occupancyRate = totalRooms > 0 ? ((occupiedRooms / totalRooms) * 100).toFixed(1) : 0;

   const today = new Date();
   today.setHours(0, 0, 0, 0); // Normalize to start of day

   const upcomingCheckIns = reservations.filter(res => {
     const checkInDate = new Date(res.checkInDate.toDate());
     checkInDate.setHours(0, 0, 0, 0);
     return checkInDate.getTime() === today.getTime() && res.status !== 'checked-out' && res.status !== 'cancelled';
   }).length;

   const upcomingCheckOuts = reservations.filter(res => {
     const checkOutDate = new Date(res.checkOutDate.toDate());
     checkOutDate.setHours(0, 0, 0, 0);
     return checkOutDate.getTime() === today.getTime() && res.status !== 'checked-out' && res.status !== 'cancelled';
   }).length;

   const totalRevenue = finances.filter(f => f.type === 'revenue').reduce((sum, f) => sum + (f.amount || 0), 0);
   const totalExpenses = finances.filter(f => f.type === 'expense').reduce((sum, f) => sum + (f.amount || 0), 0);
   const netProfit = totalRevenue - totalExpenses;

   if (!selectedHotelId && hotels.length === 0) {
     return (
       <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
         <h1 className="text-3xl font-extrabold text-gray-900 mb-4">Selamat Datang di Arrahmah Hotel Manajemen</h1>
         <p className="text-lg text-gray-700 mb-8">
           Anda belum memiliki hotel yang terdaftar. Silakan tambahkan hotel pertama Anda untuk memulai pengelolaan.
         </p>
         <button
           onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
           className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
         >
           <i className="fas fa-plus mr-2"></i> Tambah Hotel Baru
         </button>
       </div>
     );
   }

   if (!selectedHotelId) {
     return (
       <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
         <h1 className="text-3xl font-extrabold text-gray-900 mb-4">Pilih Hotel untuk Memulai</h1>
         <p className="text-lg text-gray-700 mb-8">
           Silakan pilih hotel dari daftar di navigasi samping atau tambahkan hotel baru.
         </p>
         <button
           onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
           className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
         >
           <i className="fas fa-plus mr-2"></i> Tambah Hotel Baru
         </button>
       </div>
     );
   }

   return (
     <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
       <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Dashboard Hotel: {hotels.find(h => h.id === selectedHotelId)?.name || 'Memuat...'}</h1>

       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
         {/* Card: Total Kamar */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Total Kamar</p>
             <p className="text-3xl font-bold text-gray-900">{totalRooms}</p>
           </div>
           <span className="text-blue-500 text-4xl">
             <i className="fas fa-bed"></i> {/* Font Awesome icon */}
           </span>
         </div>

         {/* Card: Kamar Tersedia */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Kamar Tersedia</p>
             <p className="text-3xl font-bold text-gray-900">{availableRooms}</p>
           </div>
           <span className="text-green-500 text-4xl">
             <i className="fas fa-door-open"></i> {/* Font Awesome icon */}
           </span>
         </div>

         {/* Card: Tingkat Hunian */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Tingkat Hunian</p>
             <p className="text-3xl font-bold text-gray-900">{occupancyRate}%</p>
           </div>
           <span className="text-yellow-500 text-4xl">
             <i className="fas fa-chart-pie"></i> {/* Font Awesome icon */}
           </span>
         </div>

         {/* Card: Check-in Hari Ini */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Check-in Hari Ini</p>
             <p className="text-3xl font-bold text-gray-900">{upcomingCheckIns}</p>
           </div>
           <span className="text-purple-500 text-4xl">
             <i className="fas fa-sign-in-alt"></i> {/* Font Awesome icon */}
           </span>
         </div>

         {/* Card: Check-out Hari Ini */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Check-out Hari Ini</p>
             <p className="text-3xl font-bold text-gray-900">{upcomingCheckOuts}</p>
           </div>
           <span className="text-red-500 text-4xl">
             <i className="fas fa-sign-out-alt"></i> {/* Font Awesome icon */}
           </span>
         </div>

         {/* Card: Total Pendapatan */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Total Pendapatan</p>
             <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalRevenue)}</p>
           </div>
           <span className="text-teal-500 text-4xl">
             <i className="fas fa-money-bill-wave"></i> {/* Font Awesome icon */}
           </span>
         </div>

         {/* Card: Total Pengeluaran */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Total Pengeluaran</p>
             <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalExpenses)}</p>
           </div>
           <span className="text-orange-500 text-4xl">
             <i className="fas fa-file-invoice-dollar"></i> {/* Font Awesome icon */}
           </span>
         </div>

         {/* Card: Laba Bersih */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
           <div>
             <p className="text-sm font-medium text-gray-500">Laba Bersih</p>
             <p className={`text-3xl font-bold ${netProfit >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
             {formatCurrency(netProfit)}
           </p>
         </div>
         <span className="text-indigo-500 text-4xl">
           <i className="fas fa-wallet"></i> {/* Font Awesome icon */}
         </span>
       </div>
     </div>

       {/* Section for Quick Actions / Recent Activity */}
       <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
         <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
           <h2 className="text-xl font-bold text-gray-800 mb-4">Reservasi Mendatang</h2>
           {reservations.filter(res => {
             const checkInDate = new Date(res.checkInDate.toDate());
             checkInDate.setHours(0, 0, 0, 0);
             return checkInDate >= today && res.status !== 'checked-out' && res.status !== 'cancelled';
           }).sort((a, b) => new Date(a.checkInDate.toDate()) - new Date(b.checkInDate.toDate()))
           .slice(0, 5) // Show top 5
           .map(res => (
             <div key={res.id} className="border-b border-gray-200 py-3 last:border-b-0">
               <p className="font-semibold text-gray-800">{res.guestName} - Kamar {res.roomNumber}</p>
               <p className="text-sm text-gray-600">Check-in: {formatDate(res.checkInDate)}</p>
             </div>
           ))}
           {reservations.filter(res => new Date(res.checkInDate.toDate()) >= today && res.status !== 'checked-out' && res.status !== 'cancelled').length === 0 && (
             <p className="text-gray-500">Tidak ada reservasi mendatang.</p>
           )}
         </div>

         <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
           <h2 className="text-xl font-bold text-gray-800 mb-4">Ketersediaan Kamar</h2>
           <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
             {rooms.sort((a,b) => parseInt(a.roomNumber) - parseInt(b.roomNumber)).map(room => (
               <div key={room.id} className={`p-3 rounded-md text-center shadow-sm
                 ${room.status === 'available' ? 'bg-green-100 text-green-800' :
                   room.status === 'occupied' ? 'bg-red-100 text-red-800' :
                   'bg-yellow-100 text-yellow-800'
                 }`}>
                 <p className="font-bold text-lg">{room.roomNumber}</p>
                 <p className="text-sm">{room.status === 'available' ? 'Tersedia' : room.status === 'occupied' ? 'Terisi' : 'Perawatan'}</p>
               </div>
             ))}
             {rooms.length === 0 && <p className="text-gray-500 col-span-2">Tidak ada kamar terdaftar.</p>}
           </div>
         </div>
       </div>
     </div>
   );
 }, [rooms, reservations, finances, hotels, selectedHotelId]); // Memoize Dashboard to re-render only when these data change

 // --- Reservations Component ---
 const Reservations = useCallback(() => {
   const [searchTerm, setSearchTerm] = useState('');
   const [filterStatus, setFilterStatus] = useState('all');

   const handleEdit = (res) => {
     setCurrentReservation(res);
     setShowReservationModal(true);
   };

   const handleDeleteClick = (resId) => {
     setConfirmMessage('Apakah Anda yakin ingin menghapus reservasi ini?');
     setConfirmAction(() => () => deleteReservation(resId));
     setShowConfirmModal(true);
   };

   const handleViewDetails = (res) => {
     setCurrentReservation(res);
     setShowReservationDetailModal(true);
   };

   // Filter and search logic
   const filteredReservations = reservations.filter(res => {
     const matchesSearch = searchTerm === '' ||
       res.guestName.toLowerCase().includes(searchTerm.toLowerCase()) ||
       res.roomNumber.toLowerCase().includes(searchTerm.toLowerCase());

     const matchesStatus = filterStatus === 'all' || res.status === filterStatus;

     return matchesSearch && matchesStatus;
   });

   // Sort reservations by check-in date
   const sortedReservations = [...filteredReservations].sort((a, b) => {
     const dateA = a.checkInDate?.toDate ? a.checkInDate.toDate() : new Date(a.checkInDate);
     const dateB = b.checkInDate?.toDate ? b.checkInDate.toDate() : new Date(b.checkInDate);
     return dateA - dateB;
   });

   if (!selectedHotelId) {
     return (
       <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
         <p className="text-lg text-gray-700">Silakan pilih hotel untuk melihat dan mengelola reservasi.</p>
       </div>
     );
   }


   return (
     <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
       <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Manajemen Reservasi</h1>

       <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
         <input
           type="text"
           placeholder="Cari nama tamu atau nomor kamar..."
           value={searchTerm}
           onChange={(e) => setSearchTerm(e.target.value)}
           className="w-full sm:w-1/2 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
         />
         <select
           value={filterStatus}
           onChange={(e) => setFilterStatus(e.target.value)}
           className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
         >
           <option value="all">Semua Status</option>
           <option value="confirmed">Dikonfirmasi</option>
           <option value="checked-in">Check-in</option>
           <option value="checked-out">Check-out</option>
           <option value="cancelled">Dibatalkan</option>
         </select>
         <button
           onClick={() => { setCurrentReservation(null); setShowReservationModal(true); }}
           className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
         >
           Tambah Reservasi
         </button>
       </div>

       <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-100">
         <table className="min-w-full divide-y divide-gray-200">
           <thead className="bg-gray-50">
             <tr>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Nama Tamu
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Kamar
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Check-in
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Check-out
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Tamu
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Paket Makanan
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Status
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Tindakan
               </th>
             </tr>
           </thead>
           <tbody className="bg-white divide-y divide-gray-200">
             {sortedReservations.length === 0 ? (
               <tr>
                 <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                   Tidak ada reservasi yang ditemukan.
                 </td>
               </tr>
             ) : (
               sortedReservations.map((res) => (
                 <tr key={res.id}>
                   <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{res.guestName}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{res.roomNumber}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatDate(res.checkInDate)}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatDate(res.checkOutDate)}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{res.numberOfGuests}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{res.mealPlan}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm">
                     <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                       ${res.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                         res.status === 'checked-in' ? 'bg-green-100 text-green-800' :
                         res.status === 'checked-out' ? 'bg-gray-100 text-gray-800' :
                         'bg-red-100 text-red-800'
                       }`}>
                       {res.status === 'confirmed' ? 'Dikonfirmasi' :
                        res.status === 'checked-in' ? 'Check-in' :
                        res.status === 'checked-out' ? 'Check-out' :
                        'Dibatalkan'}
                     </span>
                   </td>
                   <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex items-center gap-2">
                     <button
                       onClick={() => handleViewDetails(res)}
                       className="text-blue-600 hover:text-blue-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
                       title="Lihat Detail/Invoice"
                     >
                       <i className="fas fa-info-circle"></i>
                     </button>
                     <button
                       onClick={() => handleEdit(res)}
                       className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75"
                       title="Edit Reservasi"
                     >
                       <i className="fas fa-edit"></i>
                     </button>
                     <button
                       onClick={() => handleDeleteClick(res.id)}
                       className="text-red-600 hover:text-red-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75"
                       title="Hapus Reservasi"
                     >
                       <i className="fas fa-trash-alt"></i>
                     </button>
                   </td>
                 </tr>
               ))
             )}
           </tbody>
         </table>
       </div>
       {showReservationModal && (
         <ReservationModal
           reservation={currentReservation}
           rooms={rooms}
           onClose={() => setShowReservationModal(false)}
           onSave={(id, data) => id ? updateReservation(id, data) : addReservation(data)}
           showMessage={showMessage}
         />
       )}
       {showReservationDetailModal && (
         <ReservationDetailModal
           reservation={currentReservation}
           onClose={() => setShowReservationDetailModal(false)}
           hotels={hotels} // Pass hotels for hotel details
           selectedHotelId={selectedHotelId} // Pass selected hotel ID
         />
       )}
     </div>
   );
 }, [reservations, rooms, addReservation, updateReservation, deleteReservation, selectedHotelId, hotels]); // Memoize Reservations

 // --- Rooms Component ---
 const Rooms = useCallback(() => {
   const [searchTerm, setSearchTerm] = useState('');
   const [filterStatus, setFilterStatus] = useState('all');
   const [filterType, setFilterType] = useState('all');

   const handleEdit = (room) => {
     setCurrentRoom(room);
     setShowRoomModal(true);
   };

   const handleDeleteClick = (roomId) => {
     setConfirmMessage('Apakah Anda yakin ingin menghapus kamar ini? Ini akan menghapus semua data terkait.');
     setConfirmAction(() => () => deleteRoom(roomId));
     setShowConfirmModal(true);
   };

   // Filter and search logic
   const filteredRooms = rooms.filter(room => {
     const matchesSearch = searchTerm === '' ||
       room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
       room.roomType.toLowerCase().includes(searchTerm.toLowerCase());

     const matchesStatus = filterStatus === 'all' || room.status === filterStatus;
     const matchesType = filterType === 'all' || room.roomType === filterType;

     return matchesSearch && matchesStatus && matchesType;
   });

   const sortedRooms = [...filteredRooms].sort((a,b) => parseInt(a.roomNumber) - parseInt(b.roomNumber));

   if (!selectedHotelId) {
     return (
       <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
         <p className="text-lg text-gray-700">Silakan pilih hotel untuk melihat dan mengelola kamar.</p>
       </div>
     );
   }

   return (
     <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
       <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Manajemen Kamar</h1>

       <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
         <input
           type="text"
           placeholder="Cari nomor kamar atau tipe..."
           value={searchTerm}
           onChange={(e) => setSearchTerm(e.target.value)}
           className="w-full sm:w-1/3 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
         />
         <select
           value={filterStatus}
           onChange={(e) => setFilterStatus(e.target.value)}
           className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
         >
           <option value="all">Semua Status</option>
           <option value="available">Tersedia</option>
           <option value="occupied">Terisi</option>
           <option value="maintenance">Perawatan</option>
         </select>
         <select
           value={filterType}
           onChange={(e) => setFilterType(e.target.value)}
           className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
         >
           <option value="all">Semua Tipe</option>
           <option value="Standard">Standard</option>
           <option value="Double">Double</option>
           <option value="Twin">Twin</option>
           <option value="Triple">Triple</option>
           <option value="Quad">Quad</option>
           <option value="Suite">Suite</option>
         </select>
         <button
           onClick={() => { setCurrentRoom(null); setShowRoomModal(true); }}
           className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
         >
           Tambah Kamar
         </button>
       </div>

       <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-100">
         <table className="min-w-full divide-y divide-gray-200">
           <thead className="bg-gray-50">
             <tr>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Nomor Kamar
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Tipe Kamar
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Kapasitas
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Harga per Malam
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Status
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Tindakan
               </th>
             </tr>
           </thead>
           <tbody className="bg-white divide-y divide-gray-200">
             {sortedRooms.length === 0 ? (
               <tr>
                 <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                   Tidak ada kamar yang ditemukan.
                 </td>
               </tr>
             ) : (
               sortedRooms.map((room) => (
                 <tr key={room.id}>
                   <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{room.roomNumber}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{room.roomType}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{room.capacity}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatCurrency(room.pricePerNight)}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm">
                     <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                       ${room.status === 'available' ? 'bg-green-100 text-green-800' :
                         room.status === 'occupied' ? 'bg-red-100 text-red-800' :
                         'bg-yellow-100 text-yellow-800'
                       }`}>
                       {room.status === 'available' ? 'Tersedia' :
                        room.status === 'occupied' ? 'Terisi' :
                        'Perawatan'}
                     </span>
                   </td>
                   <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex items-center gap-2">
                     <button
                       onClick={() => handleEdit(room)}
                       className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75"
                       title="Edit Kamar"
                     >
                       <i className="fas fa-edit"></i>
                     </button>
                     <button
                       onClick={() => handleDeleteClick(room.id)}
                       className="text-red-600 hover:text-red-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75"
                       title="Hapus Kamar"
                     >
                       <i className="fas fa-trash-alt"></i>
                     </button>
                   </td>
                 </tr>
               ))
             )}
           </tbody>
         </table>
       </div>
       {showRoomModal && (
         <RoomModal
           room={currentRoom}
           onClose={() => setShowRoomModal(false)}
           onSave={(id, data) => id ? updateRoom(id, data) : addRoom(data)}
           showMessage={showMessage}
         />
       )}
     </div>
   );
 }, [rooms, addRoom, updateRoom, deleteRoom, selectedHotelId]); // Memoize Rooms

 // --- Finance Component ---
 const Finance = useCallback(() => {
   const [newEntry, setNewEntry] = useState({
     date: new Date().toISOString().split('T')[0],
     type: 'revenue',
     description: '',
     amount: 0,
   });
   const [filterType, setFilterType] = useState('all');
   const [filterStartDate, setFilterStartDate] = useState('');
   const [filterEndDate, setFilterEndDate] = useState('');


   const handleEntryChange = (e) => {
     const { name, value, type } = e.target;
     setNewEntry(prev => ({
       ...prev,
       [name]: type === 'number' ? parseFloat(value) || 0 : value
     }));
   };

   const handleAddEntry = (e) => {
     e.preventDefault();
     if (!newEntry.description.trim()) {
       showMessage('Deskripsi tidak boleh kosong.', 'error');
       return;
     }
     if (newEntry.amount <= 0) {
       showMessage('Jumlah harus lebih besar dari 0.', 'error');
       return;
     }
     const entryDate = new Date(newEntry.date);
     if (isNaN(entryDate.getTime())) {
         showMessage('Tanggal tidak valid.', 'error');
         return;
     }
     addFinanceEntry(newEntry);
     setNewEntry({
       date: new Date().toISOString().split('T')[0],
       type: 'revenue',
       description: '',
       amount: 0,
     });
   };

   const handleDeleteClick = (entryId) => {
     setConfirmMessage('Apakah Anda yakin ingin menghapus entri keuangan ini?');
     setConfirmAction(() => () => deleteFinanceEntry(entryId));
     setShowConfirmModal(true);
   };

   // Filter finances
   const filteredFinances = finances.filter(f => {
     const matchesType = filterType === 'all' || f.type === filterType;

     const entryDate = f.date?.toDate ? f.date.toDate() : new Date(f.date);
     let matchesStartDate = true;
     let matchesEndDate = true;

     if (filterStartDate) {
       const start = new Date(filterStartDate);
       start.setHours(0,0,0,0);
       matchesStartDate = entryDate >= start;
     }
     if (filterEndDate) {
       const end = new Date(filterEndDate);
       end.setHours(23,59,59,999);
       matchesEndDate = entryDate <= end;
     }

     return matchesType && matchesStartDate && matchesEndDate;
   });

   const totalRevenue = filteredFinances.filter(f => f.type === 'revenue').reduce((sum, f) => sum + (f.amount || 0), 0);
   const totalExpenses = filteredFinances.filter(f => f.type === 'expense').reduce((sum, f) => sum + (f.amount || 0), 0);
   const netProfit = totalRevenue - totalExpenses;


   // Sort finances by date, newest first
   const sortedFinances = [...filteredFinances].sort((a, b) => {
     const dateA = a.date?.toDate ? a.date.toDate() : new Date(a.date);
     const dateB = b.date?.toDate ? b.date.toDate() : new Date(b.date);
     return dateB - dateA;
   });

   if (!selectedHotelId) {
     return (
       <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
         <p className="text-lg text-gray-700">Silakan pilih hotel untuk melihat dan mengelola keuangan.</p>
       </div>
     );
   }

   return (
     <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
       <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Laporan Keuangan</h1>

       <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
         {/* Revenue Card */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
           <p className="text-sm font-medium text-gray-500">Total Pendapatan</p>
           <p className="text-3xl font-bold text-green-600">{formatCurrency(totalRevenue)}</p>
         </div>
         {/* Expenses Card */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
           <p className="text-sm font-medium text-gray-500">Total Pengeluaran</p>
           <p className="text-3xl font-bold text-red-600">{formatCurrency(totalExpenses)}</p>
         </div>
         {/* Net Profit Card */}
         <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
           <p className="text-sm font-medium text-gray-500">Laba Bersih</p>
           <p className={`text-3xl font-bold ${netProfit >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
             {formatCurrency(netProfit)}
           </p>
         </div>
       </div>

       <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 mb-8">
         <h2 className="text-xl font-bold text-gray-800 mb-4">Tambah Entri Keuangan Baru</h2>
         <form onSubmit={handleAddEntry} className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
           <div>
             <label htmlFor="financeDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal</label>
             <input
               type="date"
               id="financeDate"
               name="date"
               value={newEntry.date}
               onChange={handleEntryChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               required
             />
           </div>
           <div>
             <label htmlFor="financeType" className="block text-sm font-medium text-gray-700 mb-1">Tipe</label>
             <select
               id="financeType"
               name="type"
               value={newEntry.type}
               onChange={handleEntryChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="revenue">Pendapatan</option>
               <option value="expense">Pengeluaran</option>
             </select>
           </div>
           <div>
             <label htmlFor="financeDescription" className="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
             <input
               type="text"
               id="financeDescription"
               name="description"
               value={newEntry.description}
               onChange={handleEntryChange}
               placeholder="Mis: Pembayaran Reservasi, Biaya Perawatan"
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               required
             />
           </div>
           <div>
             <label htmlFor="financeAmount" className="block text-sm font-medium text-gray-700 mb-1">Jumlah</label>
             <input
               type="number"
               id="financeAmount"
               name="amount"
               value={newEntry.amount}
               onChange={handleEntryChange}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
               min="0"
               step="0.01"
               required
             />
           </div>
           <div className="md:col-span-4 flex justify-end">
             <button
               type="submit"
               className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
             >
               Tambah Entri
             </button>
           </div>
         </form>
       </div>

       <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 mb-8">
         <h2 className="text-xl font-bold text-gray-800 mb-4">Filter Riwayat Keuangan</h2>
         <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
           <div>
             <label htmlFor="filterType" className="block text-sm font-medium text-gray-700 mb-1">Tipe</label>
             <select
               id="filterType"
               value={filterType}
               onChange={(e) => setFilterType(e.target.value)}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="all">Semua Tipe</option>
               <option value="revenue">Pendapatan</option>
               <option value="expense">Pengeluaran</option>
             </select>
           </div>
           <div>
             <label htmlFor="filterStartDate" className="block text-sm font-medium text-gray-700 mb-1">Dari Tanggal</label>
             <input
               type="date"
               id="filterStartDate"
               value={filterStartDate}
               onChange={(e) => setFilterStartDate(e.target.value)}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             />
           </div>
           <div>
             <label htmlFor="filterEndDate" className="block text-sm font-medium text-gray-700 mb-1">Sampai Tanggal</label>
             <input
               type="date"
               id="filterEndDate"
               value={filterEndDate}
               onChange={(e) => setFilterEndDate(e.target.value)}
               className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             />
           </div>
         </div>
       </div>

       <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-100">
         <h2 className="text-xl font-bold text-gray-800 p-6 border-b border-gray-200">Riwayat Keuangan</h2>
         <table className="min-w-full divide-y divide-gray-200">
           <thead className="bg-gray-50">
             <tr>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Tanggal
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Tipe
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Deskripsi
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Jumlah
               </th>
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Tindakan
               </th>
             </tr>
           </thead>
           <tbody className="bg-white divide-y divide-gray-200">
             {sortedFinances.length === 0 ? (
               <tr>
                 <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                   Tidak ada entri keuangan yang ditemukan.
                 </td>
               </tr>
             ) : (
               sortedFinances.map((entry) => (
                 <tr key={entry.id}>
                   <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{formatDate(entry.date)}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                     <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                       ${entry.type === 'revenue' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                       {entry.type === 'revenue' ? 'Pendapatan' : 'Pengeluaran'}
                     </span>
                   </td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{entry.description}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatCurrency(entry.amount)}</td>
                   <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                     <button
                       onClick={() => handleDeleteClick(entry.id)}
                       className="text-red-600 hover:text-red-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75"
                     >
                       Hapus
                     </button>
                   </td>
                 </tr>
               ))
             )}
           </tbody>
         </table>
       </div>
     </div>
   );
 }, [finances, addFinanceEntry, deleteFinanceEntry, selectedHotelId]); // Memoize Finance

 // --- Main Layout ---
 if (!isAuthReady) {
   return (
     <div className="flex items-center justify-center min-h-screen bg-gray-100">
       <div className="text-center text-gray-700">
         <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
         <p className="text-xl font-semibold">Memuat aplikasi dan mengautentikasi...</p>
       </div>
     </div>
   );
 }

 return (
   <AuthContext.Provider value={{ userId, db, auth }}>
     <div className="font-inter antialiased flex flex-col min-h-screen bg-gray-100 text-gray-900">
       {/* Font Awesome CDN for icons */}
       <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />

       {/* Global styles for Inter font */}
       <style>
         {`
         @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
         body { font-family: 'Inter', sans-serif; }
         .scroll-container {
           -webkit-overflow-scrolling: touch;
         }
         /* Custom scrollbar for better UX */
         .scroll-container::-webkit-scrollbar {
             width: 8px;
             height: 8px;
         }
         .scroll-container::-webkit-scrollbar-track {
             background: #f1f1f1;
             border-radius: 10px;
         }
         .scroll-container::-webkit-scrollbar-thumb {
             background: #888;
             border-radius: 10px;
         }
         .scroll-container::-webkit-scrollbar-thumb:hover {
             background: #555;
         }
         `}
       </style>

       {/* Header / Top Navigation for small screens */}
       <header className="bg-white shadow-sm md:hidden sticky top-0 z-40">
         <div className="container mx-auto px-4 py-3 flex items-center justify-between">
           <h1 className="text-xl font-bold text-blue-700">Arrahmah Hotel Manajemen</h1>
           <div className="flex items-center">
             <span className="text-sm text-gray-600 mr-2 break-all">ID Pengguna: {userId}</span>
             {/* Hotel Selector for mobile */}
             <select
               value={selectedHotelId || ''}
               onChange={(e) => setSelectedHotelId(e.target.value)}
               className="ml-2 p-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
             >
               <option value="">Pilih Hotel</option>
               {hotels.map(hotel => (
                 <option key={hotel.id} value={hotel.id}>{hotel.name}</option>
               ))}
             </select>
             <button
               onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
               className="ml-2 p-2 rounded-md text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
               aria-label="Tambah Hotel"
             >
               <i className="fas fa-plus"></i>
             </button>
           </div>
         </div>
         {/* Mobile bottom navigation for easy access */}
         <nav className="bg-white border-t border-gray-200 flex justify-around py-2">
           <button
             onClick={() => setActiveTab('dashboard')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'dashboard' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-chart-line text-lg mb-1"></i>
             Dashboard
           </button>
           <button
             onClick={() => setActiveTab('reservations')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'reservations' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-book-bed text-lg mb-1"></i>
             Reservasi
           </button>
           <button
             onClick={() => setActiveTab('rooms')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'rooms' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-hotel text-lg mb-1"></i>
             Kamar
           </button>
           <button
             onClick={() => setActiveTab('finance')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'finance' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-dollar-sign text-lg mb-1"></i>
             Keuangan
           </button>
         </nav>
       </header>

       <div className="flex flex-1 overflow-hidden">
         {/* Sidebar for Desktop */}
         <aside className="hidden md:flex flex-col w-64 bg-white shadow-lg border-r border-gray-200 p-6">
           <div className="flex items-center mb-6">
             <i className="fas fa-building text-3xl text-blue-600 mr-3"></i>
             <h2 className="text-2xl font-extrabold text-gray-900">Arrahmah PMS</h2>
           </div>

           {/* Hotel Selector and Management */}
           <div className="mb-6">
             <label htmlFor="hotel-select" className="block text-sm font-medium text-gray-700 mb-2">Pilih Hotel:</label>
             <select
               id="hotel-select"
               value={selectedHotelId || ''}
               onChange={(e) => setSelectedHotelId(e.target.value)}
               className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="">-- Pilih Hotel --</option>
               {hotels.map(hotel => (
                 <option key={hotel.id} value={hotel.id}>{hotel.name}</option>
               ))}
             </select>
             <div className="mt-3 flex flex-wrap gap-2">
               <button
                 onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
                 className="flex-1 px-3 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-75"
               >
                 <i className="fas fa-plus mr-1"></i> Tambah Hotel
               </button>
               {selectedHotelId && (
                 <button
                   onClick={() => {
                     const hotelToEdit = hotels.find(h => h.id === selectedHotelId);
                     if (hotelToEdit) {
                       setCurrentHotel(hotelToEdit);
                       setShowHotelModal(true);
                     }
                   }}
                   className="flex-1 px-3 py-2 bg-yellow-500 text-white text-sm rounded-md hover:bg-yellow-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-75"
                 >
                   <i className="fas fa-edit mr-1"></i> Edit Hotel
                 </button>
               )}
               {selectedHotelId && (
                 <button
                   onClick={() => {
                     setConfirmMessage('Apakah Anda yakin ingin menghapus hotel ini? Tindakan ini tidak akan menghapus data kamar, reservasi, dan keuangan terkait secara otomatis. Silakan baca catatan penting di bawah.');
                     setConfirmAction(() => () => deleteHotel(selectedHotelId));
                     setShowConfirmModal(true);
                   }}
                   className="flex-1 px-3 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-75"
                 >
                   <i className="fas fa-trash-alt mr-1"></i> Hapus Hotel
                 </button>
               )}
             </div>
           </div>

           <nav className="flex-1 space-y-2">
             <button
               onClick={() => setActiveTab('dashboard')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'dashboard' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-chart-line mr-3 text-xl"></i>
               Dashboard
             </button>
             <button
               onClick={() => setActiveTab('reservations')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'reservations' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-book-bed mr-3 text-xl"></i>
               Reservasi
             </button>
             <button
               onClick={() => setActiveTab('rooms')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'rooms' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-hotel mr-3 text-xl"></i>
               Kamar
             </button>
             <button
               onClick={() => setActiveTab('finance')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'finance' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-dollar-sign mr-3 text-xl"></i>
               Keuangan
             </button>
           </nav>
           <div className="mt-8 text-sm text-gray-600 p-2 border-t border-gray-200 pt-4">
             <p className="font-semibold">ID Pengguna:</p>
             <p className="break-all">{userId}</p>
           </div>
         </aside>

         {/* Main Content Area */}
         <main className="flex-1 overflow-y-auto scroll-container">
           {activeTab === 'dashboard' && <Dashboard />}
           {activeTab === 'reservations' && <Reservations />}
           {activeTab === 'rooms' && <Rooms />}
           {activeTab === 'finance' && <Finance />}
         </main>
       </div>

       {/* Modals and Message Box */}
       <HotelModal
         hotel={currentHotel}
         onClose={() => setShowHotelModal(false)}
         onSave={(id, data) => id ? updateHotel(id, data) : addHotel(data)}
         showMessage={showMessage}
       />
       <ConfirmationModal
         visible={showConfirmModal}
         message={confirmMessage}
         onConfirm={() => {
           if (confirmAction) confirmAction();
           setShowConfirmModal(false);
           setConfirmAction(null);
         }}
         onCancel={() => setShowConfirmModal(false)}
       />
       <MessageBox
         message={messageBox.message}
         type={messageBox.type}
         visible={messageBox.visible}
       />
     </div>
   </AuthContext.Provider>
 );
};

export default App;