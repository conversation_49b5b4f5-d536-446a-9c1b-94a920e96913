// Utility function to format currency
export const formatCurrency = (amount) => `Rp ${amount.toLocaleString('id-ID')}`;

// Utility function to format date
export const formatDate = (timestamp) => {
  if (!timestamp) return 'N/A';
  // Firebase Timestamp to Date object
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  return date.toLocaleDateString('id-ID', { year: 'numeric', month: 'short', day: 'numeric' });
};
