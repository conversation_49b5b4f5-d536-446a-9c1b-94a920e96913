import React from 'react';

const MessageBox = ({ message, type, visible }) => {
  if (!visible) return null;

  let bgColor = 'bg-blue-500';
  let textColor = 'text-white';
  if (type === 'success') {
    bgColor = 'bg-green-500';
  } else if (type === 'error') {
    bgColor = 'bg-red-500';
  } else if (type === 'warning') {
    bgColor = 'bg-yellow-500';
    textColor = 'text-gray-800';
  }

  return (
    <div className={`fixed top-4 right-4 ${bgColor} ${textColor} p-3 rounded-lg shadow-lg z-50 transition-transform transform ${visible ? 'translate-x-0' : 'translate-x-full'}`}>
      {message}
    </div>
  );
};

export default MessageBox;
