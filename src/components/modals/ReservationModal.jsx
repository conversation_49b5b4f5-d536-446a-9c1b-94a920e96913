import React, { useState, useEffect } from 'react';
import { formatCurrency } from '../../utils/formatters';

const ReservationModal = ({ reservation, rooms, onClose, onSave, showMessage }) => {
  const today = new Date().toISOString().split('T')[0];
  const [formData, setFormData] = useState({
    guestName: reservation?.guestName || '',
    roomNumber: reservation?.roomNumber || '',
    checkInDate: reservation?.checkInDate ? new Date(reservation.checkInDate.toDate()).toISOString().split('T')[0] : today,
    checkOutDate: reservation?.checkOutDate ? new Date(reservation.checkOutDate.toDate()).toISOString().split('T')[0] : today,
    numberOfGuests: reservation?.numberOfGuests || 1,
    mealPlan: reservation?.mealPlan || 'None',
    status: reservation?.status || 'confirmed',
    totalAmount: reservation?.totalAmount || 0,
    paidAmount: reservation?.paidAmount || 0,
  });

  const [outstandingAmount, setOutstandingAmount] = useState(0);

  // Effect to calculate totalAmount when roomNumber, dates, or guests change
  useEffect(() => {
    const calculateTotalAmount = () => {
      const selectedRoom = rooms.find(r => r.roomNumber === formData.roomNumber);
      if (!selectedRoom || !formData.checkInDate || !formData.checkOutDate) {
        return 0;
      }

      const checkIn = new Date(formData.checkInDate);
      const checkOut = new Date(formData.checkOutDate);

      if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime()) || checkOut <= checkIn) {
        return 0;
      }

      const diffTime = Math.abs(checkOut - checkIn);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      let basePrice = selectedRoom.pricePerNight * diffDays;

      // Add meal plan cost (simplified: fixed cost per guest per day)
      const mealCostPerGuestPerDay = {
        'BB': 50000,   // Example: Rp 50.000 for Bed & Breakfast
        'HB': 100000,  // Example: Rp 100.000 for Half Board
        'FB': 150000,  // Example: Rp 150.000 for Full Board
        'None': 0
      };
      basePrice += (mealCostPerGuestPerDay[formData.mealPlan] || 0) * formData.numberOfGuests * diffDays;

      return basePrice;
    };

    // Only auto-calculate for new reservations or if totalAmount is 0 for existing ones
    if (!reservation || (reservation && reservation.totalAmount === 0)) {
      const calculatedAmount = calculateTotalAmount();
      setFormData(prev => ({
        ...prev,
        totalAmount: calculatedAmount,
      }));
    }

    setOutstandingAmount((parseFloat(formData.totalAmount) || 0) - (parseFloat(formData.paidAmount) || 0));

  }, [formData.roomNumber, formData.checkInDate, formData.checkOutDate, formData.numberOfGuests, formData.mealPlan, formData.totalAmount, formData.paidAmount, rooms, reservation]);


  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Basic validation
    if (!formData.guestName.trim()) {
      showMessage('Nama tamu tidak boleh kosong.', 'error');
      return;
    }
    if (!formData.roomNumber) {
      showMessage('Nomor kamar harus dipilih.', 'error');
      return;
    }
    const checkIn = new Date(formData.checkInDate);
    const checkOut = new Date(formData.checkOutDate);

    if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
      showMessage('Format tanggal check-in/check-out tidak valid.', 'error');
      return;
    }
    if (checkOut <= checkIn) {
      showMessage('Tanggal check-out harus setelah tanggal check-in.', 'error');
      return;
    }
    if (formData.numberOfGuests <= 0) {
      showMessage('Jumlah tamu harus lebih besar dari 0.', 'error');
      return;
    }
    if (formData.totalAmount < 0) {
      showMessage('Total biaya tidak boleh negatif.', 'error');
      return;
    }
    if (formData.paidAmount < 0) {
      showMessage('Jumlah dibayar tidak boleh negatif.', 'error');
      return;
    }

    onSave(reservation ? reservation.id : null, { ...formData, outstandingAmount });
  };

  // Filter available rooms (simplified: just list all for now, complex logic would check dates)
  const availableRoomNumbers = rooms.map(room => room.roomNumber);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg border border-gray-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">{reservation ? 'Edit Reservasi' : 'Tambah Reservasi Baru'}</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="guestName" className="block text-sm font-medium text-gray-700 mb-1">Nama Tamu</label>
            <input
              type="text"
              id="guestName"
              name="guestName"
              value={formData.guestName}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <label htmlFor="roomNumber" className="block text-sm font-medium text-gray-700 mb-1">Nomor Kamar</label>
            <select
              id="roomNumber"
              name="roomNumber"
              value={formData.roomNumber}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Pilih Kamar</option>
              {availableRoomNumbers.map(num => (
                <option key={num} value={num}>{num}</option>
              ))}
            </select>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="checkInDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal Check-in</label>
              <input
                type="date"
                id="checkInDate"
                name="checkInDate"
                value={formData.checkInDate}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div>
              <label htmlFor="checkOutDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal Check-out</label>
              <input
                type="date"
                id="checkOutDate"
                name="checkOutDate"
                value={formData.checkOutDate}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>
          <div>
            <label htmlFor="numberOfGuests" className="block text-sm font-medium text-gray-700 mb-1">Jumlah Tamu</label>
            <input
              type="number"
              id="numberOfGuests"
              name="numberOfGuests"
              value={formData.numberOfGuests}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="1"
              required
            />
          </div>
          <div>
            <label htmlFor="mealPlan" className="block text-sm font-medium text-gray-700 mb-1">Paket Makanan</label>
            <select
              id="mealPlan"
              name="mealPlan"
              value={formData.mealPlan}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="None">Tidak Ada</option>
              <option value="BB">BB (Bed & Breakfast)</option>
              <option value="HB">HB (Half Board)</option>
              <option value="FB">FB (Full Board)</option>
            </select>
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="confirmed">Dikonfirmasi</option>
              <option value="checked-in">Check-in</option>
              <option value="checked-out">Check-out</option>
              <option value="cancelled">Dibatalkan</option>
            </select>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="totalAmount" className="block text-sm font-medium text-gray-700 mb-1">Total Biaya</label>
              <input
                type="number"
                id="totalAmount"
                name="totalAmount"
                value={formData.totalAmount}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="0"
                step="0.01"
                required
              />
            </div>
            <div>
              <label htmlFor="paidAmount" className="block text-sm font-medium text-gray-700 mb-1">Jumlah Dibayar</label>
              <input
                type="number"
                id="paidAmount"
                name="paidAmount"
                value={formData.paidAmount}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="0"
                step="0.01"
              />
            </div>
          </div>
          <div className="text-right text-gray-700 font-semibold">
            Sisa Tunggakan: {formatCurrency(outstandingAmount)}
          </div>
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-5 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-75"
            >
              Batal
            </button>
            <button
              type="submit"
              className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
            >
              Simpan
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReservationModal;
