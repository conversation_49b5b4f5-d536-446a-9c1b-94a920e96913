import React from 'react';
import { formatCurrency, formatDate } from '../../utils/formatters';

const ReservationDetailModal = ({ reservation, onClose, hotels, selectedHotelId }) => {
  if (!reservation) return null;

  const selectedHotel = hotels.find(h => h.id === selectedHotelId);
  const hotelName = selectedHotel ? selectedHotel.name : 'N/A';
  const hotelAddress = selectedHotel ? selectedHotel.address : 'N/A';
  const hotelContact = selectedHotel ? selectedHotel.contact : 'N/A';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl border border-gray-200 overflow-y-auto max-h-[90vh]">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 border-b pb-3">Detail Reservasi & Tagihan</h2>

        <div className="mb-6 border-b pb-4">
          <h3 className="text-xl font-semibold text-blue-700 mb-2">{hotelName}</h3>
          <p className="text-gray-600 text-sm">{hotelAddress}</p>
          <p className="text-gray-600 text-sm">Telp: {hotelContact}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <p className="font-semibold text-gray-800">Detail Tamu:</p>
            <p>Nama: {reservation.guestName}</p>
            <p>Jumlah Tamu: {reservation.numberOfGuests}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-800">Detail Reservasi:</p>
            <p>Nomor Reservasi: {reservation.id}</p>
            <p>Kamar: {reservation.roomNumber}</p>
            <p>Check-in: {formatDate(reservation.checkInDate)}</p>
            <p>Check-out: {formatDate(reservation.checkOutDate)}</p>
            <p>Paket Makanan: {reservation.mealPlan}</p>
            <p>Status: {reservation.status}</p>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-3">Rincian Biaya</h3>
          <div className="space-y-2">
            <div className="flex justify-between border-b pb-1">
              <span className="text-gray-700">Total Biaya Reservasi:</span>
              <span className="font-semibold">{formatCurrency(reservation.totalAmount)}</span>
            </div>
            <div className="flex justify-between border-b pb-1">
              <span className="text-gray-700">Jumlah Dibayar:</span>
              <span className="font-semibold">{formatCurrency(reservation.paidAmount)}</span>
            </div>
            <div className="flex justify-between pt-2">
              <span className="text-lg font-bold text-gray-900">Sisa Tunggakan:</span>
              <span className="text-lg font-bold text-red-600">{formatCurrency(reservation.outstandingAmount)}</span>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <button
            onClick={onClose}
            className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
          >
            Tutup
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReservationDetailModal;
