import React, { useState } from 'react';

const HotelModal = ({ hotel, onClose, onSave, showMessage }) => {
  const [formData, setFormData] = useState({
    name: hotel?.name || '',
    address: hotel?.address || '',
    contact: hotel?.contact || '',
    description: hotel?.description || '', // Added description
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      showMessage('Nama hotel tidak boleh kosong.', 'error');
      return;
    }
    onSave(hotel ? hotel.id : null, formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg border border-gray-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">{hotel ? 'Edit Hotel' : 'Tambah Hotel Baru'}</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="hotelName" className="block text-sm font-medium text-gray-700 mb-1">Nama Hotel</label>
            <input
              type="text"
              id="hotelName"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <label htmlFor="hotelAddress" className="block text-sm font-medium text-gray-700 mb-1">Alamat</label>
            <input
              type="text"
              id="hotelAddress"
              name="address"
              value={formData.address}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label htmlFor="hotelContact" className="block text-sm font-medium text-gray-700 mb-1">Kontak</label>
            <input
              type="text"
              id="hotelContact"
              name="contact"
              value={formData.contact}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label htmlFor="hotelDescription" className="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
            <textarea
              id="hotelDescription"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows="3"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            ></textarea>
          </div>
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-5 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-75"
            >
              Batal
            </button>
            <button
              type="submit"
              className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
            >
              Simpan
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default HotelModal;
